#pragma once

#include <string>
#include <vector>
#include <memory>

#ifdef _WIN32
#include <windows.h>
#else
#include <termios.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#endif

enum class BaudRate {
    BAUD_9600 = 9600,
    BAUD_19200 = 19200,
    BAUD_38400 = 38400,
    BAUD_57600 = 57600,
    BAUD_115200 = 115200,
    BAUD_230400 = 230400,
    BAUD_460800 = 460800,
    BAUD_921600 = 921600
};

enum class DataBits {
    DATA_5 = 5,
    DATA_6 = 6,
    DATA_7 = 7,
    DATA_8 = 8
};

enum class StopBits {
    STOP_1 = 1,
    STOP_2 = 2
};

enum class Parity {
    NONE,
    ODD,
    EVEN
};

struct SerialConfig {
    BaudRate baudRate = BaudRate::BAUD_115200;
    DataBits dataBits = DataBits::DATA_8;
    StopBits stopBits = StopBits::STOP_1;
    Parity parity = Parity::NONE;
    int timeoutMs = 1000;
};

class SerialPort {
public:
    SerialPort();
    ~SerialPort();

    // 禁用拷贝构造和赋值
    SerialPort(const SerialPort&) = delete;
    SerialPort& operator=(const SerialPort&) = delete;

    // 获取可用串口列表
    static std::vector<std::string> getAvailablePorts();

    // 打开串口
    bool open(const std::string& portName, const SerialConfig& config = SerialConfig{});
    
    // 关闭串口
    void close();
    
    // 检查串口是否打开
    bool isOpen() const;
    
    // 写入数据
    bool write(const std::vector<uint8_t>& data);
    bool write(const std::string& data);
    
    // 读取数据
    std::vector<uint8_t> read(size_t maxBytes = 1024);
    std::string readString(size_t maxBytes = 1024);
    
    // 清空缓冲区
    void flush();
    
    // 获取可读字节数
    size_t available();
    
    // 获取当前配置
    const SerialConfig& getConfig() const { return config_; }
    
    // 获取端口名
    const std::string& getPortName() const { return portName_; }

private:
    bool configurePort();
    
#ifdef _WIN32
    HANDLE handle_;
#else
    int fd_;
    struct termios oldTermios_;
#endif
    
    std::string portName_;
    SerialConfig config_;
    bool isOpen_;
};
