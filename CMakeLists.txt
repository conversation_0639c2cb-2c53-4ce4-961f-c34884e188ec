cmake_minimum_required(VERSION 3.10)
project(SerialTool)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译选项
if(WIN32)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W4")
else()
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -pedantic")
endif()

# 创建源文件目录
file(GLOB_RECURSE SOURCES
    "src/*.cpp"
    "src/*.h"
)

# 创建可执行文件
add_executable(${PROJECT_NAME} ${SOURCES})

# Windows特定的链接库
if(WIN32)
    target_link_libraries(${PROJECT_NAME} PRIVATE)
else()
    # Linux/Unix系统可能需要pthread
    find_package(Threads REQUIRED)
    target_link_libraries(${PROJECT_NAME} PRIVATE Threads::Threads)
endif()

# 设置包含目录
target_include_directories(${PROJECT_NAME} PRIVATE src)

# 设置输出目录
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)
